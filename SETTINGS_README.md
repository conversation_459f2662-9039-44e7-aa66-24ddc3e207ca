# Unified Settings Configuration

This project now uses a single `settings.py` file that supports multiple environments through environment variables.

## Environment Types

### 1. Development (default)
- **Environment**: `ENVIRONMENT=development` or not set
- **Database**: SQLite (with optional PostgreSQL configuration)
- **Static Files**: Local files with WhiteNoise
- **Debug**: Enabled
- **Security**: Relaxed settings for development

### 2. Production
- **Environment**: `ENVIRONMENT=production`
- **Database**: PostgreSQL (configured via environment variables)
- **Static Files**: Local files with WhiteNoise compression
- **Debug**: Disabled
- **Security**: Enhanced security settings
- **Logging**: File-based logging

### 3. CDN-enabled Production
- **Environment**: `ENVIRONMENT=cdn`
- **Database**: PostgreSQL (configured via environment variables)
- **Static Files**: AWS S3 with optional CloudFront
- **Debug**: Disabled
- **Security**: Maximum security settings
- **Logging**: File-based logging

## Configuration

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` with your specific values:
   ```bash
   # For development (default)
   ENVIRONMENT=development

   # For production
   ENVIRONMENT=production
   SECRET_KEY=your-production-secret-key
   POSTGRES_NAME=your_db_name
   POSTGRES_USER=your_db_user
   POSTGRES_PASSWORD=your_db_password

   # For CDN environment (includes all production settings plus AWS)
   ENVIRONMENT=cdn
   AWS_ACCESS_KEY_ID=your_aws_key
   AWS_SECRET_ACCESS_KEY=your_aws_secret
   AWS_STORAGE_BUCKET_NAME=your_bucket_name
   ```

## Key Features

- **Single settings file**: No more managing multiple settings files
- **Environment-based configuration**: Automatically switches based on ENVIRONMENT variable
- **Backward compatibility**: Maintains all functionality from previous settings files
- **Security**: Appropriate security settings for each environment
- **Flexibility**: Easy to switch between local and cloud storage

## Migration from Old Settings

The old `settings_production.py` and `settings_cdn.py` files have been removed. All their functionality is now included in the unified `settings.py` file.

## Dependencies

Make sure you have the required packages installed:

```bash
# For all environments
pip install python-dotenv

# For production with WhiteNoise
pip install whitenoise

# For CDN environment
pip install django-storages boto3
```
