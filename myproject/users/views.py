import json

from django.contrib import messages
from django.contrib.auth import authenticate, login
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib.auth.tokens import default_token_generator
from django.core.mail import send_mail
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.template.loader import render_to_string
from django.urls import reverse
from django.utils.encoding import force_bytes, force_str
from django.utils.http import urlsafe_base64_decode, urlsafe_base64_encode

from .forms import (
    PasswordResetRequestForm,
    ReceiptForm,
    SignUpForm,
    UpdateProfileForm,
)
from .models import Receipt, UserProfile


def signup_view(request):
    if request.method == "POST":
        form = SignUpForm(request.POST)
        if form.is_valid():
            form.save()
            return redirect("login")
        else:
            return render(
                request, "users/signup.html", {"form": form, "errors": form.errors}
            )
    else:
        form = SignUpForm()

    return render(request, "users/signup.html", {"form": form})


def login_view(request):
    if request.method == "POST":
        username = request.POST["username"]
        password = request.POST["password"]
        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)  # Log the user in
            print("login successful")
            return redirect(
                "welcome"
            )  # Redirect to the welcome page after successful login
        else:
            # Invalid login credentials
            print("invalid credentials")
            return render(
                request, "users/login.html", {"errors": "Invalid username or password"}
            )

    return render(request, "users/login.html", {"error": None})


def home_view(request):
    return render(request, "users/home.html")


def welcome_view(request):
    username = request.user.username  # Get the username of the logged-in user
    return render(request, "users/welcome.html", {"username": username})


def update_profile(request):
    user_profile, created = UserProfile.objects.get_or_create(
        user=request.user
    )  # Get the current user's profile

    if request.method == "POST":
        form = UpdateProfileForm(request.POST, request.FILES)
        if form.is_valid():
            # Check if company logo is provided
            if not form.cleaned_data["company_logo"]:
                form.add_error("company_logo", "Company logo is required.")
                return render(request, "users/update_profile.html", {"form": form})

            # Update the user's profile with the new data
            user_profile.company_logo = form.cleaned_data["company_logo"]
            user_profile.company_name = form.cleaned_data["company_name"]
            user_profile.company_details = form.cleaned_data["company_details"]
            user_profile.address = form.cleaned_data["address"]
            user_profile.whatsapp_contact = form.cleaned_data["whatsapp_contact"]
            user_profile.save()  # Save the profile with updated information
            next_url = request.GET.get("next")
            if next_url:
                return redirect(next_url)
            return redirect("welcome")
    else:
        # Populate the form with current user's profile data
        form = UpdateProfileForm(
            initial={
                "company_logo": user_profile.company_logo,
                "company_name": user_profile.company_name,
                "company_details": user_profile.company_details,
                "address": user_profile.address,
                "whatsapp_contact": user_profile.whatsapp_contact,
            }
        )

    return render(request, "users/update_profile.html", {"form": form})


@login_required
def generate_receipt(request):
    user_profile, created = UserProfile.objects.get_or_create(user=request.user)
    if not user_profile.company_name:
        return redirect(f"{reverse('update_profile')}?next=generate_receipt")

    if request.method == "POST":
        form = ReceiptForm(request.POST)
        if form.is_valid():
            # Extract form data
            customer_name = form.cleaned_data["customer_name"]
            products_json = form.cleaned_data["products"]
            discount = form.cleaned_data["discount"]
            date_of_purchase = form.cleaned_data["date_of_purchase"]
            method_of_purchase = form.cleaned_data["method_of_purchase"]

            # Parse products JSON
            products = json.loads(products_json)

            # Calculate subtotal for each product and total
            for product in products:
                product["subtotal"] = product["quantity"] * product["price"]
            total = sum(p["subtotal"] for p in products) - float(discount)
            # save the data into the database
            receipt = Receipt.objects.create(
                user=request.user,
                customer_name=customer_name,
                products=products,
                discount=discount,
                total=total,
                payment_method=method_of_purchase,
            )
            receipt.save()

            # Prepare data for rendering receipt
            receipt_data = {
                "customer_name": customer_name,
                "company_address": user_profile.address,
                "company_contact": user_profile.whatsapp_contact,
                "products": products,
                "discount": discount if discount > 0 else "",
                "total": total,
                "date_of_purchase": date_of_purchase,
                "method_of_purchase": method_of_purchase,
                "company_logo": user_profile.company_logo.url,
                "company_name": user_profile.company_name,
            }
            return render(request, "users/receipt.html", {"receipt_data": receipt_data})
    else:
        form = ReceiptForm()
    return render(request, "users/generate_receipt.html", {"form": form})


@login_required
def dashboard(request):
    """displays the dashboard for the user"""
    # Ensure user is authenticated
    if not request.user.is_authenticated:
        return redirect('login')

    try:
        # Get filter parameters from request
        search_query = request.GET.get('search', '').strip()
        date_from = request.GET.get('date_from', '')
        date_to = request.GET.get('date_to', '')

        # Start with all user receipts
        receipts = Receipt.objects.filter(user=request.user)

        # Apply search filter (customer name, payment method)
        if search_query:
            receipts = receipts.filter(
                Q(customer_name__icontains=search_query) |
                Q(payment_method__icontains=search_query)
            )

        # Apply date range filter
        if date_from:
            from datetime import datetime
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                receipts = receipts.filter(date__date__gte=date_from_obj)
            except ValueError:
                pass  # Invalid date format, ignore

        if date_to:
            from datetime import datetime
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                receipts = receipts.filter(date__date__lte=date_to_obj)
            except ValueError:
                pass  # Invalid date format, ignore

        # Order by date (newest first)
        receipts = receipts.order_by("-date")

        # Pagination
        paginator = Paginator(receipts, 10)
        page_number = request.GET.get("page", 1)
        page_obj = paginator.get_page(page_number)

        # Prepare context
        context = {
            "receipts": page_obj,
            "username": request.user.username,
            "search_query": search_query,
            "date_from": date_from,
            "date_to": date_to,
            "total_receipts": receipts.count(),
        }

        return render(request, "users/dashboard.html", context)

    except Receipt.DoesNotExist:
        return None
    except Exception as e:
        print(f"exception: {str(e)}")
        return render(request, "users/welcome.html")


@login_required
def view_receipt(request, receipt_id):
    """View a specific receipt"""

    # Get the receipt and ensure it belongs to the current user
    receipt = get_object_or_404(Receipt, id=receipt_id, user=request.user)
    user_profile = request.user.userprofile

    # Prepare data for rendering receipt (similar to generate_receipt view)
    receipt_data = {
        "customer_name": receipt.customer_name,
        "company_address": user_profile.address,
        "company_contact": user_profile.whatsapp_contact,
        "products": receipt.products,
        "discount": receipt.discount if receipt.discount > 0 else "",
        "total": receipt.total,
        "date_of_purchase": receipt.date.strftime('%Y-%m-%d'),
        "method_of_purchase": receipt.payment_method,
        "company_logo": user_profile.company_logo.url,
        "company_name": user_profile.company_name,
    }

    return render(request, "users/receipt.html", {"receipt_data": receipt_data})


@login_required
def delete_receipt(request, receipt_id):
    """Delete a specific receipt"""

    # Only allow POST requests
    if request.method != 'POST':
        return JsonResponse({
            "success": False,
            "message": "Only POST requests allowed."
        }, status=405)

    # Get the receipt and ensure it belongs to the current user
    receipt = get_object_or_404(Receipt, id=receipt_id, user=request.user)

    try:
        receipt.delete()
        messages.success(request, "Receipt deleted successfully.")
        return JsonResponse({
            "success": True,
            "message": "Receipt deleted successfully."
        })
    except Exception as e:
        return JsonResponse({
            "success": False,
            "message": f"Error deleting receipt: {str(e)}"
        }, status=500)


def password_reset_request(request):
    """Handle password reset request"""
    if request.method == 'POST':
        form = PasswordResetRequestForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            try:
                user = User.objects.get(email=email)

                # Generate password reset token
                token = default_token_generator.make_token(user)
                uid = urlsafe_base64_encode(force_bytes(user.pk))

                # Build reset URL
                reset_url = request.build_absolute_uri(
                    reverse('password_reset_confirm', kwargs={
                        'uidb64': uid,
                        'token': token
                    })
                )

                # Prepare email content
                context = {
                    'user': user,
                    'reset_url': reset_url,
                    'site_name': 'QuickReceipt',
                }

                subject = 'Password Reset Request - QuickReceipt'
                message = render_to_string(
                    'users/password_reset_email.txt',
                    context
                )
                html_message = render_to_string(
                    'users/password_reset_email.html',
                    context
                )

                # Send email
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=None,  # Uses DEFAULT_FROM_EMAIL
                    recipient_list=[email],
                    html_message=html_message,
                    fail_silently=False,
                )

                messages.success(
                    request,
                    'Password reset instructions have been sent to your email.'
                )
                return redirect('password_reset_done')

            except User.DoesNotExist:
                # Don't reveal that the user doesn't exist
                messages.success(
                    request,
                    'If an account with that email exists, '
                    'password reset instructions have been sent.'
                )
                return redirect('password_reset_done')
            except Exception:
                messages.error(
                    request,
                    'An error occurred while sending the email. '
                    'Please try again later.'
                )
    else:
        form = PasswordResetRequestForm()

    return render(request, 'users/password_reset_request.html', {'form': form})


def password_reset_done(request):
    """Show confirmation that password reset email was sent"""
    return render(request, 'users/password_reset_done.html')


def password_reset_confirm(request, uidb64, token):
    """Handle password reset confirmation with new password"""

    from .forms import PasswordResetConfirmForm

    try:
        uid = force_str(urlsafe_base64_decode(uidb64))
        user = User.objects.get(pk=uid)
    except (TypeError, ValueError, OverflowError, User.DoesNotExist):
        user = None

    if user is not None and default_token_generator.check_token(user, token):
        if request.method == 'POST':
            form = PasswordResetConfirmForm(request.POST)
            if form.is_valid():
                # Set new password
                new_password = form.cleaned_data['new_password']
                user.set_password(new_password)
                user.save()

                # Automatically log in the user after password reset
                login(request, user)

                messages.success(
                    request,
                    'Your password has been reset successfully. '
                    'You are now logged in.'
                )
                return redirect('password_reset_complete')
        else:
            form = PasswordResetConfirmForm()

        return render(request, 'users/password_reset_confirm.html', {
            'form': form,
            'validlink': True,
            'uidb64': uidb64,
            'token': token,
        })
    else:
        return render(request, 'users/password_reset_confirm.html', {
            'validlink': False,
        })


def password_reset_complete(request):
    """Show confirmation that password was reset successfully"""
    return render(request, 'users/password_reset_complete.html')


@login_required
def logout_view(request):
    """Handle user logout"""
    from django.contrib.auth import logout

    # Clear the user session
    logout(request)

    # Add a success message
    messages.success(request, "You have been successfully logged out.")

    # Redirect to welcome page
    return redirect('welcome')
