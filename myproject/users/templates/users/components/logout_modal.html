<!-- Logout Confirmation Modal Component -->
<!-- Usage: include 'users/components/logout_modal.html' -->

<!-- Logout Confirmation Modal -->
<div class="modal fade" id="logoutModal" tabindex="-1" aria-labelledby="logoutModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content logout-modal-content">
            <div class="modal-header logout-modal-header">
                <div class="logout-icon-container">
                    <svg class="logout-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M16 17L21 12L16 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M21 12H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <h5 class="modal-title logout-modal-title" id="logoutModalLabel">Confirm Logout</h5>
                <button type="button" class="btn-close logout-btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body logout-modal-body">
                <div class="logout-warning-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <p class="logout-message">Are you sure you want to log out?</p>
                <p class="logout-submessage">You will need to sign in again to access your dashboard.</p>
            </div>
            <div class="modal-footer logout-modal-footer">
                <button type="button" class="btn logout-cancel-btn" data-bs-dismiss="modal">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    Cancel
                </button>
                <form id="logoutForm" method="POST" action="{% url 'logout' %}" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn logout-confirm-btn" id="confirmLogoutBtn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M16 17L21 12L16 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M21 12H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span class="logout-btn-text">Log Out</span>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    /* Logout Modal Styling */
    .logout-modal-content {
        background: #fafbfc;
        border: none;
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        overflow: hidden;
    }

    .logout-modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-bottom: none;
        padding: 1.5rem;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .logout-icon-container {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        padding: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .logout-icon {
        color: white;
    }

    .logout-modal-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        flex-grow: 1;
    }

    .logout-btn-close {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 1;
        transition: all 0.3s ease;
    }

    .logout-btn-close:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }

    .logout-modal-body {
        padding: 2rem;
        text-align: center;
    }

    .logout-warning-icon {
        margin-bottom: 1rem;
        color: #f39c12;
    }

    .logout-message {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .logout-submessage {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    .logout-modal-footer {
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        padding: 1rem 1.5rem;
        display: flex;
        gap: 12px;
        justify-content: flex-end;
    }

    .logout-cancel-btn {
        background: #6c757d;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-size: 0.9rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
    }

    .logout-cancel-btn:hover {
        background: #5a6268;
        color: white;
        transform: translateY(-1px);
    }

    .logout-confirm-btn {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-size: 0.9rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .logout-confirm-btn:hover {
        background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
    }

    .logout-confirm-btn:active {
        transform: translateY(0);
    }

    .logout-confirm-btn.loading .logout-btn-text {
        opacity: 0;
    }

    .logout-confirm-btn.loading::after {
        content: '';
        position: absolute;
        width: 16px;
        height: 16px;
        border: 2px solid transparent;
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Modal Animation */
    .modal.fade .modal-dialog {
        transform: scale(0.8) translateY(-50px);
        transition: all 0.3s ease;
    }

    .modal.show .modal-dialog {
        transform: scale(1) translateY(0);
    }

    /* Responsive Design */
    @media (max-width: 576px) {
        .logout-modal-header {
            padding: 1rem;
        }

        .logout-modal-body {
            padding: 1.5rem;
        }

        .logout-modal-footer {
            padding: 1rem;
            flex-direction: column;
        }

        .logout-cancel-btn,
        .logout-confirm-btn {
            width: 100%;
            justify-content: center;
        }

        .logout-modal-title {
            font-size: 1.1rem;
        }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
        .logout-modal-content {
            background: #2c3e50;
            color: #ecf0f1;
        }

        .logout-modal-footer {
            background: #34495e;
            border-top-color: #4a5f7a;
        }

        .logout-message {
            color: #ecf0f1;
        }

        .logout-submessage {
            color: #bdc3c7;
        }
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .logout-modal-content {
            border: 2px solid #000;
        }

        .logout-modal-header {
            background: #000;
            color: #fff;
        }

        .logout-confirm-btn {
            background: #d32f2f;
            border: 2px solid #000;
        }

        .logout-cancel-btn {
            background: #424242;
            border: 2px solid #000;
        }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .modal.fade .modal-dialog,
        .logout-cancel-btn,
        .logout-confirm-btn,
        .logout-btn-close {
            transition: none;
        }

        .logout-confirm-btn.loading::after {
            animation: none;
        }
    }
</style>
