<!-- Back Button Component -->
<!-- Usage: include 'users/components/back_button.html' with fallback_url='dashboard' button_text='Back' show_icon=True -->

{% load static %}

<div class="back-button-container" id="backButtonContainer">
  <button type="button"
    class="back-button"
    id="backButton"
    data-fallback-url="{% if fallback_url %}
      {% url fallback_url %}
    {% else %}
      {% url 'dashboard' %}
    {% endif %}">
    {% if show_icon|default:True %}
      <svg class="back-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
    {% endif %}
    <span class="back-text">{{ button_text|default:'Back' }}</span>
  </button>
</div>

<style>
  .back-button-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    margin-bottom: 0;
  }
  
  .back-button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    color: #666;
    font-size: 0.8em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
  }
  
  .back-button:hover {
    background-color: rgba(248, 249, 250, 0.98);
    border-color: #667eea;
    color: #667eea;
    transform: translateX(-1px);
  }
  
  .back-button:active {
    transform: translateX(0);
  }
  
  .back-icon {
    transition: transform 0.3s ease;
    width: 14px;
    height: 14px;
  }
  
  .back-button:hover .back-icon {
    transform: translateX(-1px);
  }
  
  .back-text {
    font-family: inherit;
  }
  
  /* Responsive design */
  @media (max-width: 768px) {
    .back-button-container {
      top: 15px;
      left: 15px;
    }
  
    .back-button {
      padding: 6px 10px;
      font-size: 0.75em;
      gap: 4px;
    }
  
    .back-icon {
      width: 12px;
      height: 12px;
    }
  }
  
  /* Hide during download */
  .hide-for-download {
    display: none !important;
  }
  
  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .back-button {
      background: rgba(45, 55, 72, 0.95);
      border-color: #4a5568;
      color: #a0aec0;
    }
  
    .back-button:hover {
      background-color: rgba(45, 55, 72, 0.98);
      border-color: #667eea;
      color: #667eea;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const backButton = document.getElementById('backButton')
  
    if (backButton) {
      backButton.addEventListener('click', function () {
        const fallbackUrl = this.getAttribute('data-fallback-url')
  
        // Check if there's browser history to go back to
        if (window.history.length > 1 && document.referrer) {
          // Check if the referrer is from the same domain
          const referrerDomain = new URL(document.referrer).hostname
          const currentDomain = window.location.hostname
  
          if (referrerDomain === currentDomain) {
            window.history.back()
            return
          }
        }
  
        // Fallback to the specified URL
        window.location.href = fallbackUrl
      })
    }
  })
</script>
