# users/urls.py

from django.urls import path

from .views import (
    dashboard,
    delete_receipt,
    generate_receipt,
    login_view,
    logout_view,
    password_reset_complete,
    password_reset_confirm,
    password_reset_done,
    password_reset_request,
    signup_view,
    update_profile,
    view_receipt,
    welcome_view,
)

urlpatterns = [
    path("signup/", signup_view, name="signup"),
    path("login/", login_view, name="login"),
    path("logout/", logout_view, name="logout"),
    path("welcome/", welcome_view, name="welcome"),
    path("update_profile/", update_profile, name="update_profile"),  # New URL
    path("generate_receipt/", generate_receipt, name="generate_receipt"),
    path("dashboard/", dashboard, name="dashboard"),
    path("receipt/<int:receipt_id>/", view_receipt, name="view_receipt"),
    path("receipt/<int:receipt_id>/delete/", delete_receipt,
         name="delete_receipt"),
    # Password Reset URLs
    path("password-reset/", password_reset_request,
         name="password_reset_request"),
    path("password-reset/done/", password_reset_done,
         name="password_reset_done"),
    path("password-reset/confirm/<uidb64>/<token>/", password_reset_confirm,
         name="password_reset_confirm"),
    path("password-reset/complete/", password_reset_complete,
         name="password_reset_complete"),
]
