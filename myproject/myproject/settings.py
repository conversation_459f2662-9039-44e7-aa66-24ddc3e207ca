"""
Django settings for myproject project.

This unified settings file supports multiple environments:
- Development (default)
- Production (ENVIRONMENT=production)
- CDN-enabled Production (ENVIRONMENT=cdn)

Environment is controlled by the ENVIRONMENT variable in your .env file.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""
import os
from pathlib import Path

from dotenv import load_dotenv

load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Environment detection
ENVIRONMENT = os.getenv('ENVIRONMENT', 'development').lower()

# SECURITY WARNING: keep the secret key used in production secret!
if ENVIRONMENT == 'development':
    SECRET_KEY = 'django-insecure-7qzo%-xolf*=@9^r+wmg+5#mx78v3*#tb077cug#zd*^(y26zt'
    DEBUG = True
else:
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')
    DEBUG = False

# Allowed hosts configuration
if ENVIRONMENT == 'development':
    ALLOWED_HOSTS = [
        '*',
        'localhost',
        '127.0.0.1',
        '************',
        'ec2-54-147-42-55.compute-1.amazonaws.com',
        'http://ec2-54-147-42-55.compute-1.amazonaws.com',
        'http://************'
    ]
elif ENVIRONMENT == 'production':
    ALLOWED_HOSTS = [
        'localhost',
        'quickreceipt.store',
        'www.quickreceipt.store',
        '127.0.0.1',
        '************',
        'ec2-54-147-42-55.compute-1.amazonaws.com',
    ]
else:  # CDN environment
    ALLOWED_HOSTS = [
        '*',
        'localhost',
        '127.0.0.1',
        '************',
        'ec2-54-147-42-55.compute-1.amazonaws.com',
        'http://ec2-54-147-42-55.compute-1.amazonaws.com',
        'http://************'
    ]


# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'users',
]

# Add storages for CDN environment
if ENVIRONMENT == 'cdn':
    INSTALLED_APPS.append('storages')

# Middleware configuration
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# Add WhiteNoise for development and production (not CDN)
if ENVIRONMENT in ['development', 'production']:
    MIDDLEWARE.insert(1, 'whitenoise.middleware.WhiteNoiseMiddleware')

ROOT_URLCONF = 'myproject.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],  # Global templates folder],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'myproject.wsgi.application'


# Database configuration
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

if ENVIRONMENT == 'development':
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
            'OPTIONS': {
                'timeout': 20,
            }
        },
        'postgres': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': 'user_auth_db',
            'USER': 'postgres',
            'PASSWORD': 'abiodun',
            'HOST': 'localhost',
            'PORT': '5432',
        }
    }
else:  # Production and CDN environments use PostgreSQL
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': os.getenv('POSTGRES_NAME', 'myproject'),
            'USER': os.getenv('POSTGRES_USER', 'myproject'),
            'PASSWORD': os.getenv('POSTGRES_PASSWORD', 'myproject'),
            'HOST': os.getenv('POSTGRES_HOST', 'localhost'),
            'PORT': '5432',
        }
    }



# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static and Media files configuration
# https://docs.djangoproject.com/en/5.0/howto/static-files/

if ENVIRONMENT == 'cdn':
    # AWS S3 Configuration for Static Files
    AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
    AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
    AWS_STORAGE_BUCKET_NAME = os.getenv('AWS_STORAGE_BUCKET_NAME', 'your-bucket-name')
    AWS_S3_REGION_NAME = os.getenv('AWS_S3_REGION_NAME', 'us-east-1')
    AWS_S3_CUSTOM_DOMAIN = f'{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com'

    # Static files configuration for S3
    STATIC_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/static/'
    STATICFILES_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'

    # Media files configuration for S3
    MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/media/'
    DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'

    # S3 settings
    AWS_DEFAULT_ACL = 'public-read'
    AWS_S3_OBJECT_PARAMETERS = {
        'CacheControl': 'max-age=86400',  # 1 day cache
    }
    AWS_LOCATION = 'static'

else:
    # Local static files configuration (development and production)
    STATIC_URL = '/static/'
    STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
    MEDIA_URL = '/media/'
    MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

    # WhiteNoise configuration for production
    if ENVIRONMENT == 'production':
        STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Session configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.db'

# Security settings for production environments
if ENVIRONMENT in ['production', 'cdn']:
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    X_FRAME_OPTIONS = 'DENY'

    if ENVIRONMENT == 'cdn':
        SESSION_COOKIE_SECURE = True
        CSRF_COOKIE_SECURE = True
    elif ENVIRONMENT == 'production':
        # SESSION_COOKIE_SECURE = True  # Uncomment for HTTPS
        # CSRF_COOKIE_SECURE = True     # Uncomment for HTTPS
        CSRF_TRUSTED_ORIGINS = [
            'http://ec2-54-147-42-55.compute-1.amazonaws.com',
            'http://************',
            'https://quickreceipt.store',
            'https://www.quickreceipt.store'
        ]

# Email Configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER', '<EMAIL>')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD', 'your-app-password')
DEFAULT_FROM_EMAIL = 'QuickReceipt <<EMAIL>>'

# Password Reset Settings
PASSWORD_RESET_TIMEOUT = 3600  # 1 hour in seconds

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Logging configuration for production environments
if ENVIRONMENT in ['production', 'cdn']:
    LOGGING = {
        'version': 1,
        'disable_existing_loggers': False,
        'handlers': {
            'file': {
                'level': 'INFO',
                'class': 'logging.FileHandler',
                'filename': '/var/log/django/myproject.log',
            },
        },
        'loggers': {
            'django': {
                'handlers': ['file'],
                'level': 'INFO',
                'propagate': True,
            },
        },
    }
